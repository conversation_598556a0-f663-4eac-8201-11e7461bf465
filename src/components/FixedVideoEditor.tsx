'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import {
  Play,
  Pause,
  SkipBack,
  SkipForward,
  Volume2,
  VolumeX,
  Scissors,
  Download,
  FileVideo,
  Minimize2,
  Maximize2
} from 'lucide-react'
import toast from 'react-hot-toast'

interface FixedVideoEditorProps {
  file: File
  onEditComplete: (blob: Blob) => void
}

export function FixedVideoEditor({ file, onEditComplete }: FixedVideoEditorProps) {
  console.log('🎬 FixedVideoEditor rendered with file:', file?.name)
  
  const videoRef = useRef<HTMLVideoElement>(null)
  
  // Basic video state
  const [isLoading, setIsLoading] = useState(true)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const [videoUrl, setVideoUrl] = useState<string>('')
  const [error, setError] = useState<string>('')

  // Editing state
  const [startTime, setStartTime] = useState(0)
  const [endTime, setEndTime] = useState(0)

  // Initialize video URL
  useEffect(() => {
    if (!file) return

    console.log('🎬 Creating video URL for:', file.name)
    const url = URL.createObjectURL(file)
    console.log('📹 Video URL created:', url)
    setVideoUrl(url)

    return () => {
      console.log('🧹 Cleaning up video URL')
      URL.revokeObjectURL(url)
    }
  }, [file])

  // Handle video metadata loaded
  const handleLoadedMetadata = useCallback(() => {
    console.log('🎉 Video metadata loaded!')
    const video = videoRef.current
    if (!video) {
      console.error('❌ Video ref not available')
      return
    }

    console.log('📊 Video duration:', video.duration)
    console.log('📐 Video dimensions:', video.videoWidth, 'x', video.videoHeight)
    
    if (video.duration && !isNaN(video.duration)) {
      setDuration(video.duration)
      setEndTime(video.duration)
      setIsLoading(false)
      setError('')
      console.log('✅ Video is ready!')
    } else {
      console.error('❌ Invalid video duration:', video.duration)
      setError('Invalid video file')
      setIsLoading(false)
    }
  }, [])

  // Handle video error
  const handleVideoError = useCallback((e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    console.error('❌ Video error:', e)
    const video = e.currentTarget
    const videoError = video.error
    
    let errorMessage = 'Unknown error'
    if (videoError) {
      switch (videoError.code) {
        case videoError.MEDIA_ERR_ABORTED:
          errorMessage = 'Video loading was aborted'
          break
        case videoError.MEDIA_ERR_NETWORK:
          errorMessage = 'Network error while loading video'
          break
        case videoError.MEDIA_ERR_DECODE:
          errorMessage = 'Video format not supported or corrupted'
          break
        case videoError.MEDIA_ERR_SRC_NOT_SUPPORTED:
          errorMessage = 'Video format not supported'
          break
        default:
          errorMessage = videoError.message || 'Unknown video error'
      }
    }
    
    console.error('Video error details:', videoError)
    setError(errorMessage)
    setIsLoading(false)
    toast.error('Failed to load video: ' + errorMessage)
  }, [])

  // Handle time update
  const handleTimeUpdate = useCallback(() => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime)
    }
  }, [])

  // Toggle play/pause
  const togglePlay = useCallback(() => {
    const video = videoRef.current
    if (!video) return

    if (isPlaying) {
      video.pause()
    } else {
      video.play()
    }
  }, [isPlaying])

  // Seek to specific time
  const seekTo = useCallback((time: number) => {
    const video = videoRef.current
    if (!video) return

    video.currentTime = Math.max(0, Math.min(time, duration))
  }, [duration])

  // Skip backward/forward
  const skipBackward = useCallback(() => {
    seekTo(currentTime - 10)
  }, [currentTime, seekTo])

  const skipForward = useCallback(() => {
    seekTo(currentTime + 10)
  }, [currentTime, seekTo])

  // Handle volume change
  const handleVolumeChange = useCallback((newVolume: number) => {
    const video = videoRef.current
    if (!video) return

    setVolume(newVolume)
    video.volume = newVolume
    setIsMuted(newVolume === 0)
  }, [])

  // Toggle mute
  const toggleMute = useCallback(() => {
    const video = videoRef.current
    if (!video) return

    if (isMuted) {
      video.volume = volume
      setIsMuted(false)
    } else {
      video.volume = 0
      setIsMuted(true)
    }
  }, [isMuted, volume])

  // Format time display
  const formatTime = useCallback((seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    const ms = Math.floor((seconds % 1) * 100)
    return `${mins}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(2, '0')}`
  }, [])

  // Handle trim start/end changes
  const handleTrimStartChange = useCallback((time: number) => {
    setStartTime(Math.max(0, Math.min(time, endTime - 0.1)))
  }, [endTime])

  const handleTrimEndChange = useCallback((time: number) => {
    setEndTime(Math.max(startTime + 0.1, Math.min(time, duration)))
  }, [startTime, duration])

  // Export original video (placeholder)
  const handleExportOriginal = useCallback(() => {
    const blob = new Blob([file], { type: file.type })
    onEditComplete(blob)
    toast.success('Original video exported successfully!')
  }, [file, onEditComplete])

  if (error) {
    return (
      <div className="w-full h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">❌</div>
          <p className="text-white text-lg mb-2">Video Error</p>
          <p className="text-red-400 text-sm">{error}</p>
          <p className="text-gray-400 text-xs mt-4">File: {file.name}</p>
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="w-full h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-white text-lg">Loading video...</p>
          <p className="text-gray-400 text-sm mt-2">File: {file.name}</p>
          <p className="text-gray-400 text-xs mt-1">Size: {(file.size / 1024 / 1024).toFixed(2)} MB</p>
          {videoUrl && (
            <p className="text-green-400 text-xs mt-1">Video URL created ✓</p>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="w-full h-screen bg-gray-900 text-white flex flex-col">
      {/* Header */}
      <div className="h-14 bg-gray-800 border-b border-gray-700 flex items-center justify-between px-4">
        <div className="flex items-center space-x-3">
          <FileVideo className="w-5 h-5 text-blue-400" />
          <span className="font-medium truncate max-w-xs">{file.name}</span>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Video Preview */}
        <div className="flex-1 bg-black flex flex-col">
          {/* Video Container */}
          <div className="flex-1 flex items-center justify-center p-4">
            <div className="relative max-w-full max-h-full">
              <video
                ref={videoRef}
                src={videoUrl}
                className="max-w-full max-h-full object-contain"
                onLoadedMetadata={handleLoadedMetadata}
                onTimeUpdate={handleTimeUpdate}
                onPlay={() => setIsPlaying(true)}
                onPause={() => setIsPlaying(false)}
                onError={handleVideoError}
                onLoadStart={() => console.log('📥 Load started')}
                onCanPlay={() => console.log('✅ Can play')}
                playsInline
                preload="metadata"
              />
              
              {/* Video Overlay Controls */}
              <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                <button
                  onClick={togglePlay}
                  className="w-16 h-16 bg-black/50 rounded-full flex items-center justify-center hover:bg-black/70 transition-colors"
                >
                  {isPlaying ? <Pause className="w-8 h-8" /> : <Play className="w-8 h-8 ml-1" />}
                </button>
              </div>
            </div>
          </div>

          {/* Video Controls */}
          <div className="h-20 bg-gray-800 border-t border-gray-700 flex items-center justify-between px-6">
            <div className="flex items-center space-x-4">
              <button onClick={skipBackward} className="p-2 hover:bg-gray-700 rounded">
                <SkipBack className="w-5 h-5" />
              </button>
              <button onClick={togglePlay} className="p-3 bg-blue-600 hover:bg-blue-700 rounded-full">
                {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5 ml-0.5" />}
              </button>
              <button onClick={skipForward} className="p-2 hover:bg-gray-700 rounded">
                <SkipForward className="w-5 h-5" />
              </button>
            </div>

            <div className="flex items-center space-x-4">
              {/* Time Display */}
              <div className="text-sm font-mono bg-gray-700 px-2 py-1 rounded">
                {formatTime(currentTime)} / {formatTime(duration)}
              </div>

              {/* Volume Control */}
              <div className="flex items-center space-x-2">
                <button onClick={toggleMute} className="p-1 hover:bg-gray-700 rounded">
                  {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
                </button>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={isMuted ? 0 : volume}
                  onChange={(e) => handleVolumeChange(Number(e.target.value))}
                  className="w-20 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Right Panel - Tools */}
        <div className="w-80 bg-gray-800 border-l border-gray-700 flex flex-col">
          {/* Panel Header */}
          <div className="h-12 border-b border-gray-700 flex items-center justify-center">
            <span className="text-sm font-medium">Video Tools</span>
          </div>

          {/* Panel Content */}
          <div className="flex-1 p-4 overflow-y-auto">
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">Video Trimming</h3>
              
              {/* Trim Controls */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Start Time: {formatTime(startTime)}
                  </label>
                  <input
                    type="range"
                    min={0}
                    max={duration}
                    step={0.1}
                    value={startTime}
                    onChange={(e) => handleTrimStartChange(Number(e.target.value))}
                    className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">
                    End Time: {formatTime(endTime)}
                  </label>
                  <input
                    type="range"
                    min={0}
                    max={duration}
                    step={0.1}
                    value={endTime}
                    onChange={(e) => handleTrimEndChange(Number(e.target.value))}
                    className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                  />
                </div>
              </div>

              {/* Quick Actions */}
              <div className="space-y-2">
                <button
                  onClick={() => seekTo(startTime)}
                  className="w-full px-3 py-2 bg-green-600 hover:bg-green-700 rounded text-sm"
                >
                  Go to Start
                </button>
                <button
                  onClick={() => seekTo(endTime)}
                  className="w-full px-3 py-2 bg-green-600 hover:bg-green-700 rounded text-sm"
                >
                  Go to End
                </button>
              </div>

              {/* Export */}
              <div className="pt-4 border-t border-gray-700">
                <div className="text-sm text-gray-400 mb-3">
                  Trim Duration: {formatTime(endTime - startTime)}
                </div>
                <button
                  onClick={handleExportOriginal}
                  className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded flex items-center justify-center space-x-2"
                >
                  <Download className="w-4 h-4" />
                  <span>Export Video</span>
                </button>
              </div>

              {/* Video Info */}
              <div className="pt-4 border-t border-gray-700">
                <h4 className="text-sm font-medium mb-3">Video Information</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Duration:</span>
                    <span>{formatTime(duration)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">File Size:</span>
                    <span>{(file.size / 1024 / 1024).toFixed(2)} MB</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Format:</span>
                    <span>{file.type}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
