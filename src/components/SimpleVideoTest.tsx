'use client'

import { useState, useRef, useEffect } from 'react'
import { Play, Pause } from 'lucide-react'

interface SimpleVideoTestProps {
  file: File
}

export function SimpleVideoTest({ file }: SimpleVideoTestProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isPlaying, setIsPlaying] = useState(false)
  const [duration, setDuration] = useState(0)
  const [currentTime, setCurrentTime] = useState(0)
  const [videoUrl, setVideoUrl] = useState<string>('')
  const [error, setError] = useState<string>('')

  useEffect(() => {
    console.log('🎬 SimpleVideoTest: Initializing with file:', file.name, file.type, file.size)
    
    const url = URL.createObjectURL(file)
    console.log('📹 Created URL:', url)
    setVideoUrl(url)

    return () => {
      console.log('🧹 Cleaning up URL')
      URL.revokeObjectURL(url)
    }
  }, [file])

  const handleLoadedMetadata = () => {
    console.log('🎉 Video metadata loaded!')
    const video = videoRef.current
    if (video) {
      console.log('📊 Duration:', video.duration)
      console.log('📐 Dimensions:', video.videoWidth, 'x', video.videoHeight)
      setDuration(video.duration)
      setIsLoading(false)
    }
  }

  const handleError = (e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    console.error('❌ Video error:', e)
    const video = e.currentTarget
    const errorMsg = video.error?.message || 'Unknown error'
    console.error('Error details:', video.error)
    setError(errorMsg)
    setIsLoading(false)
  }

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime)
    }
  }

  const togglePlay = () => {
    const video = videoRef.current
    if (!video) return

    if (isPlaying) {
      video.pause()
    } else {
      video.play()
    }
  }

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div className="w-full h-screen bg-gray-900 text-white p-8">
      <h1 className="text-2xl font-bold mb-4">Simple Video Test</h1>
      
      <div className="mb-4 text-sm">
        <p>File: {file.name}</p>
        <p>Type: {file.type}</p>
        <p>Size: {(file.size / 1024 / 1024).toFixed(2)} MB</p>
        <p>Video URL: {videoUrl ? '✓ Created' : '❌ Not created'}</p>
        <p>Loading: {isLoading ? 'Yes' : 'No'}</p>
        {error && <p className="text-red-400">Error: {error}</p>}
      </div>

      {isLoading && (
        <div className="mb-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-2"></div>
          <p>Loading video...</p>
        </div>
      )}

      <div className="bg-black rounded-lg overflow-hidden mb-4" style={{ maxWidth: '800px', maxHeight: '600px' }}>
        <video
          ref={videoRef}
          src={videoUrl}
          className="w-full h-auto"
          onLoadedMetadata={handleLoadedMetadata}
          onTimeUpdate={handleTimeUpdate}
          onPlay={() => setIsPlaying(true)}
          onPause={() => setIsPlaying(false)}
          onError={handleError}
          onLoadStart={() => console.log('📥 Load started')}
          onCanPlay={() => console.log('✅ Can play')}
          onLoadedData={() => console.log('📊 Data loaded')}
          playsInline
          preload="metadata"
          controls
        />
      </div>

      {!isLoading && !error && (
        <div className="space-y-4">
          <div className="flex items-center space-x-4">
            <button
              onClick={togglePlay}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded flex items-center space-x-2"
            >
              {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              <span>{isPlaying ? 'Pause' : 'Play'}</span>
            </button>
            
            <div className="text-sm">
              {formatTime(currentTime)} / {formatTime(duration)}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
