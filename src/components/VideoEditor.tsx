'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import {
  Play,
  Pause,
  Skip<PERSON><PERSON>,
  Ski<PERSON>For<PERSON>,
  Volume2,
  VolumeX,
  Scissors,
  Download,
  Music,
  Save,
  RotateCw,
  Crop,
  Sliders,
  Droplets,
  ZoomIn,
  ZoomOut,
  Square,
  Circle,
  Info,
  Clock,
  FileVideo,
  Settings,
  Maximize2,
  Minimize2
} from 'lucide-react'
import toast from 'react-hot-toast'
import { WatermarkSettings, WatermarkConfig, defaultWatermarkConfig, applyWatermarkToCanvas } from './WatermarkSettings'
import { memoryManager, PerformanceMonitor, createManagedURL, createManagedBlob } from '@/utils/memoryManager'

interface VideoEditorProps {
  file: File
  onEditComplete: (blob: Blob) => void
}

interface VideoSettings {
  startTime: number
  endTime: number
  volume: number
  brightness: number
  contrast: number
  saturation: number
  speed: number
}

interface TimelineFrame {
  time: number
  thumbnail: string
  index: number
}

interface PlaybackSpeed {
  value: number
  label: string
}

export function VideoEditor({ file, onEditComplete }: VideoEditorProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const [activeTab, setActiveTab] = useState<'trim' | 'audio' | 'effects' | 'watermark'>('trim')
  const [isProcessing, setIsProcessing] = useState(false)
  const [watermarkConfig, setWatermarkConfig] = useState<WatermarkConfig>(defaultWatermarkConfig)
  const [managedResources, setManagedResources] = useState<string[]>([])

  const [settings, setSettings] = useState<VideoSettings>({
    startTime: 0,
    endTime: 0,
    volume: 1,
    brightness: 0,
    contrast: 0,
    saturation: 0,
    speed: 1
  })

  // Initialize video
  useEffect(() => {
    if (!videoRef.current) return

    const video = videoRef.current
    const url = URL.createObjectURL(file)
    video.src = url

    const handleLoadedMetadata = () => {
      try {
        if (video.duration && !isNaN(video.duration)) {
          setDuration(video.duration)
          setSettings(prev => ({ ...prev, endTime: video.duration }))
          setIsLoading(false)
        } else {
          throw new Error('Invalid video duration')
        }
      } catch (error) {
        console.error('Error loading video metadata:', error)
        setIsLoading(false)
        toast.error('Failed to load video metadata')
      }
    }

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime)
    }

    const handlePlay = () => setIsPlaying(true)
    const handlePause = () => setIsPlaying(false)

    const handleError = () => {
      console.error('Video loading error')
      setIsLoading(false)
      toast.error('Failed to load video file')
    }

    video.addEventListener('loadedmetadata', handleLoadedMetadata)
    video.addEventListener('timeupdate', handleTimeUpdate)
    video.addEventListener('play', handlePlay)
    video.addEventListener('pause', handlePause)
    video.addEventListener('error', handleError)

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata)
      video.removeEventListener('timeupdate', handleTimeUpdate)
      video.removeEventListener('play', handlePlay)
      video.removeEventListener('pause', handlePause)
      video.removeEventListener('error', handleError)
      URL.revokeObjectURL(url)
      // Clean up managed resources
      managedResources.forEach(id => memoryManager.release(id))
    }
  }, [file])

  // Apply video effects
  useEffect(() => {
    if (!videoRef.current || !canvasRef.current) return

    const video = videoRef.current
    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    
    if (!ctx) return

    const applyEffects = () => {
      if (video.paused || video.ended) return

      canvas.width = video.videoWidth
      canvas.height = video.videoHeight

      // Draw video frame
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height)

      // Apply filters
      if (settings.brightness !== 0 || settings.contrast !== 0 || settings.saturation !== 0) {
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
        const data = imageData.data

        for (let i = 0; i < data.length; i += 4) {
          // Brightness
          data[i] += settings.brightness * 2.55
          data[i + 1] += settings.brightness * 2.55
          data[i + 2] += settings.brightness * 2.55

          // Contrast
          const contrast = (settings.contrast + 100) / 100
          data[i] = ((data[i] / 255 - 0.5) * contrast + 0.5) * 255
          data[i + 1] = ((data[i + 1] / 255 - 0.5) * contrast + 0.5) * 255
          data[i + 2] = ((data[i + 2] / 255 - 0.5) * contrast + 0.5) * 255

          // Clamp values
          data[i] = Math.max(0, Math.min(255, data[i]))
          data[i + 1] = Math.max(0, Math.min(255, data[i + 1]))
          data[i + 2] = Math.max(0, Math.min(255, data[i + 2]))
        }

        ctx.putImageData(imageData, 0, 0)
      }

      // Apply watermark
      if (watermarkConfig.enabled) {
        applyWatermarkToCanvas(canvas, watermarkConfig)
      }

      requestAnimationFrame(applyEffects)
    }

    if (isPlaying) {
      applyEffects()
    }
  }, [isPlaying, settings.brightness, settings.contrast, settings.saturation, watermarkConfig])

  const togglePlay = () => {
    if (!videoRef.current) return

    if (isPlaying) {
      videoRef.current.pause()
    } else {
      videoRef.current.play()
    }
  }

  const seekTo = (time: number) => {
    if (!videoRef.current) return
    videoRef.current.currentTime = time
    setCurrentTime(time)
  }

  const handleVolumeChange = (newVolume: number) => {
    if (!videoRef.current) return
    
    setVolume(newVolume)
    setSettings(prev => ({ ...prev, volume: newVolume }))
    videoRef.current.volume = newVolume
    
    if (newVolume === 0) {
      setIsMuted(true)
    } else {
      setIsMuted(false)
    }
  }

  const toggleMute = () => {
    if (!videoRef.current) return
    
    if (isMuted) {
      videoRef.current.volume = volume
      setIsMuted(false)
    } else {
      videoRef.current.volume = 0
      setIsMuted(true)
    }
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  const exportVideo = async () => {
    if (!videoRef.current) return

    setIsProcessing(true)

    try {
      await PerformanceMonitor.measureAsync('video-export', async () => {
        // For now, we'll create a simple blob from the current video
        // In a real implementation, you would use FFmpeg.wasm to process the video
        const response = await fetch(videoRef.current!.src)
        const blob = await response.blob()

        // Create managed blob
        const { blob: managedBlob, id: blobId } = createManagedBlob([blob])
        setManagedResources(prev => [...prev, blobId])

        // Simulate processing time
        await new Promise(resolve => setTimeout(resolve, 2000))

        onEditComplete(managedBlob)
        toast.success('Video exported successfully!')
      })
    } catch (error) {
      console.error('Export failed:', error)
      toast.error('Failed to export video')
    } finally {
      setIsProcessing(false)
    }
  }

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
        <div className="flex items-center justify-center h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
      {/* Video Player */}
      <div className="relative bg-black">
        <video
          ref={videoRef}
          className="w-full h-auto max-h-96 object-contain"
          onClick={togglePlay}
        />
        <canvas
          ref={canvasRef}
          className="absolute inset-0 w-full h-full object-contain pointer-events-none"
          style={{ display: settings.brightness !== 0 || settings.contrast !== 0 || settings.saturation !== 0 ? 'block' : 'none' }}
        />
        
        {/* Play/Pause Overlay */}
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <button
            onClick={togglePlay}
            className="w-16 h-16 bg-black/50 rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors pointer-events-auto"
          >
            {isPlaying ? <Pause className="w-8 h-8" /> : <Play className="w-8 h-8 ml-1" />}
          </button>
        </div>
      </div>

      {/* Controls */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        {/* Timeline */}
        <div className="mb-4">
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-600 dark:text-gray-400 w-12">
              {formatTime(currentTime)}
            </span>
            <div className="flex-1 relative">
              <input
                type="range"
                min={0}
                max={duration}
                value={currentTime}
                onChange={(e) => seekTo(Number(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
              />
              {/* Trim markers */}
              <div
                className="absolute top-0 h-2 bg-blue-500 rounded-lg pointer-events-none"
                style={{
                  left: `${(settings.startTime / duration) * 100}%`,
                  width: `${((settings.endTime - settings.startTime) / duration) * 100}%`
                }}
              />
            </div>
            <span className="text-sm text-gray-600 dark:text-gray-400 w-12">
              {formatTime(duration)}
            </span>
          </div>
        </div>

        {/* Player Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <button
              onClick={() => seekTo(Math.max(0, currentTime - 10))}
              className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              <SkipBack className="w-4 h-4" />
            </button>
            <button
              onClick={togglePlay}
              className="p-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700"
            >
              {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            </button>
            <button
              onClick={() => seekTo(Math.min(duration, currentTime + 10))}
              className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              <SkipForward className="w-4 h-4" />
            </button>
          </div>

          {/* Volume Control */}
          <div className="flex items-center space-x-2">
            <button
              onClick={toggleMute}
              className="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
            </button>
            <input
              type="range"
              min={0}
              max={1}
              step={0.1}
              value={isMuted ? 0 : volume}
              onChange={(e) => handleVolumeChange(Number(e.target.value))}
              className="w-20 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
            />
          </div>

          {/* Export Button */}
          <button
            onClick={exportVideo}
            disabled={isProcessing}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isProcessing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Processing...</span>
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                <span>Export</span>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <div className="flex space-x-1 p-4">
          {[
            { id: 'trim', label: 'Trim', icon: Scissors },
            { id: 'audio', label: 'Audio', icon: Music },
            { id: 'effects', label: 'Effects', icon: Sliders },
            { id: 'watermark', label: 'Watermark', icon: Droplets }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                  : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <div className="p-4">
        {activeTab === 'trim' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Trim Video</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Start Time: {formatTime(settings.startTime)}
                </label>
                <input
                  type="range"
                  min={0}
                  max={duration}
                  value={settings.startTime}
                  onChange={(e) => {
                    const value = Number(e.target.value)
                    setSettings(prev => ({ 
                      ...prev, 
                      startTime: Math.min(value, prev.endTime - 1)
                    }))
                  }}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  End Time: {formatTime(settings.endTime)}
                </label>
                <input
                  type="range"
                  min={0}
                  max={duration}
                  value={settings.endTime}
                  onChange={(e) => {
                    const value = Number(e.target.value)
                    setSettings(prev => ({ 
                      ...prev, 
                      endTime: Math.max(value, prev.startTime + 1)
                    }))
                  }}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                />
              </div>
            </div>
            
            <div className="flex space-x-2">
              <button
                onClick={() => seekTo(settings.startTime)}
                className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
              >
                Go to Start
              </button>
              <button
                onClick={() => seekTo(settings.endTime)}
                className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
              >
                Go to End
              </button>
            </div>
          </div>
        )}

        {activeTab === 'audio' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Audio Settings</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Volume: {Math.round(settings.volume * 100)}%
              </label>
              <input
                type="range"
                min={0}
                max={1}
                step={0.1}
                value={settings.volume}
                onChange={(e) => handleVolumeChange(Number(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Playback Speed: {settings.speed}x
              </label>
              <input
                type="range"
                min={0.5}
                max={2}
                step={0.1}
                value={settings.speed}
                onChange={(e) => {
                  const speed = Number(e.target.value)
                  setSettings(prev => ({ ...prev, speed }))
                  if (videoRef.current) {
                    videoRef.current.playbackRate = speed
                  }
                }}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
              />
            </div>
          </div>
        )}

        {activeTab === 'effects' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Video Effects</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Brightness: {settings.brightness}
              </label>
              <input
                type="range"
                min={-100}
                max={100}
                value={settings.brightness}
                onChange={(e) => setSettings(prev => ({ ...prev, brightness: Number(e.target.value) }))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Contrast: {settings.contrast}
              </label>
              <input
                type="range"
                min={-100}
                max={100}
                value={settings.contrast}
                onChange={(e) => setSettings(prev => ({ ...prev, contrast: Number(e.target.value) }))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Saturation: {settings.saturation}
              </label>
              <input
                type="range"
                min={-100}
                max={100}
                value={settings.saturation}
                onChange={(e) => setSettings(prev => ({ ...prev, saturation: Number(e.target.value) }))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
              />
            </div>
            
            <button
              onClick={() => setSettings(prev => ({
                ...prev,
                brightness: 0,
                contrast: 0,
                saturation: 0
              }))}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
            >
              Reset Effects
            </button>
          </div>
        )}

        {activeTab === 'watermark' && (
          <WatermarkSettings
            config={watermarkConfig}
            onChange={setWatermarkConfig}
          />
        )}
      </div>
    </div>
  )
}
