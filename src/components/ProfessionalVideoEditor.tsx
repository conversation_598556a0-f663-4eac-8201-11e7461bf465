'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import {
  Play,
  Pause,
  SkipB<PERSON>,
  SkipForward,
  Volume2,
  VolumeX,
  Scissors,
  Download,
  ZoomIn,
  ZoomOut,
  Info,
  Clock,
  FileVideo,
  Settings,
  Maximize2,
  Minimize2,
  RotateCcw,
  RotateCw,
  Square
} from 'lucide-react'
import toast from 'react-hot-toast'
import { FFmpeg } from '@ffmpeg/ffmpeg'
import { fetchFile, toBlobURL } from '@ffmpeg/util'

interface ProfessionalVideoEditorProps {
  file: File
  onEditComplete: (blob: Blob) => void
}

interface TimelineFrame {
  time: number
  thumbnail: string
  index: number
}

interface PlaybackSpeed {
  value: number
  label: string
}

const PLAYBACK_SPEEDS: PlaybackSpeed[] = [
  { value: 0.5, label: '0.5x' },
  { value: 1, label: '1x' },
  { value: 1.5, label: '1.5x' },
  { value: 2, label: '2x' }
]

const TIMELINE_ZOOM_LEVELS = [25, 50, 75, 100, 150, 200, 300, 400]

export function ProfessionalVideoEditor({ file, onEditComplete }: ProfessionalVideoEditorProps) {
  console.log('🎬 ProfessionalVideoEditor rendered with file:', file?.name)

  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const timelineRef = useRef<HTMLDivElement>(null)
  const frameCanvasRef = useRef<HTMLCanvasElement>(null)
  const ffmpegRef = useRef<FFmpeg>(new FFmpeg())

  // Basic video state
  const [isLoading, setIsLoading] = useState(true)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const [playbackSpeed, setPlaybackSpeed] = useState(1)
  const [videoUrl, setVideoUrl] = useState<string>('')

  // Timeline state
  const [timelineFrames, setTimelineFrames] = useState<TimelineFrame[]>([])
  const [timelineZoom, setTimelineZoom] = useState(100)
  const [isGeneratingFrames, setIsGeneratingFrames] = useState(false)

  // Editing state
  const [startTime, setStartTime] = useState(0)
  const [endTime, setEndTime] = useState(0)
  const [isProcessing, setIsProcessing] = useState(false)
  const [isFFmpegLoaded, setIsFFmpegLoaded] = useState(false)
  const [processingProgress, setProcessingProgress] = useState(0)

  // UI state
  const [activePanel, setActivePanel] = useState<'trim' | 'info'>('trim')
  const [isFullscreen, setIsFullscreen] = useState(false)

  // Drag state
  const [isDragging, setIsDragging] = useState<'start' | 'end' | null>(null)
  const [dragStartX, setDragStartX] = useState(0)
  const [dragStartTime, setDragStartTime] = useState(0)

  // Initialize FFmpeg
  useEffect(() => {
    const loadFFmpeg = async () => {
      try {
        const ffmpeg = ffmpegRef.current
        const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd'

        await ffmpeg.load({
          coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
          wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
        })

        ffmpeg.on('progress', ({ progress }) => {
          setProcessingProgress(Math.round(progress * 100))
        })

        setIsFFmpegLoaded(true)
        toast.success('Video editor ready!')
      } catch (error) {
        console.error('Failed to load FFmpeg:', error)
        toast.error('Failed to load video editor')
      }
    }

    loadFFmpeg()
  }, [])

  // Initialize video - simplified approach
  useEffect(() => {
    if (!file) return

    console.log('🎬 Initializing video with file:', file.name, file.type, file.size)

    // Create video URL
    const url = URL.createObjectURL(file)
    console.log('📹 Created video URL:', url)

    // Set the URL immediately
    setVideoUrl(url)

    // Set a timeout to handle loading failures
    const loadTimeout = setTimeout(() => {
      if (isLoading) {
        console.warn('⚠️ Video loading timeout after 15 seconds')
        setIsLoading(false)
        toast.error('Video loading timeout. The file might be corrupted or in an unsupported format.')
      }
    }, 15000)

    return () => {
      console.log('🧹 Cleaning up video URL and timeout')
      clearTimeout(loadTimeout)
      URL.revokeObjectURL(url)
      setVideoUrl('')
    }
  }, [file, isLoading])

        // Generate timeline frame thumbnails
        const generateTimelineFrames = useCallback(async () => {
          const video = videoRef.current
          const canvas = frameCanvasRef.current
          const videoDuration = video?.duration || duration
      
          console.log('🎞️ Generating timeline frames, duration:', videoDuration)
      
          if (!video || !canvas || !videoDuration || videoDuration <= 0) {
            console.log('❌ Cannot generate frames - missing:', {
              video: !!video,
              canvas: !!canvas,
              duration: videoDuration,
              videoReady: video?.readyState
            })
            return
          }
      
          setIsGeneratingFrames(true)
          const frames: TimelineFrame[] = []
          const frameCount = Math.min(10, Math.max(5, Math.floor(videoDuration))) // 5-10 frames
          const interval = videoDuration / frameCount
      
          const ctx = canvas.getContext('2d')
          if (!ctx) return
      
          canvas.width = 160
          canvas.height = 90
      
          for (let i = 0; i < frameCount; i++) {
            const time = i * interval
      
            try {
              // Seek to time
              video.currentTime = time
      
              // Wait for seek to complete
              await new Promise<void>((resolve) => {
                const onSeeked = () => {
                  video.removeEventListener('seeked', onSeeked)
                  resolve()
                }
                video.addEventListener('seeked', onSeeked)
              })
      
              // Draw frame to canvas
              ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
      
              // Convert to data URL
              const thumbnail = canvas.toDataURL('image/jpeg', 0.7)
      
              frames.push({
                time,
                thumbnail,
                index: i
              })
            } catch (error) {
              console.error('Error generating frame thumbnail:', error)
            }
          }
      
          console.log('✅ Generated', frames.length, 'timeline frames')
          setTimelineFrames(frames)
          setIsGeneratingFrames(false)
      
          // Reset video to start
          video.currentTime = 0
        }, [duration])
        
  // Handle video metadata loaded
  const handleLoadedMetadata = useCallback(() => {
    console.log('🎉 Video metadata loaded!')
    const video = videoRef.current
    if (!video) {
      console.error('❌ Video ref not available in handleLoadedMetadata')
      return
    }

    console.log('📊 Video duration:', video.duration)
    console.log('📐 Video dimensions:', video.videoWidth, 'x', video.videoHeight)
    console.log('📱 Video readyState:', video.readyState)
    console.log('🎬 Video networkState:', video.networkState)



    if (video.duration && !isNaN(video.duration) && video.duration > 0) {
      setDuration(video.duration)
      setEndTime(video.duration)
      setIsLoading(false)
      console.log('✅ Video is ready! Duration:', video.duration)

      // Generate timeline frames after state is updated
      setTimeout(() => {
        if (video.duration > 0) {
          generateTimelineFrames()
        }
      }, 200)
    } else {
      console.error('❌ Invalid video duration:', video.duration)
      // Don't set loading to false yet, wait a bit more
      setTimeout(() => {
        if (video.duration && !isNaN(video.duration) && video.duration > 0) {
          setDuration(video.duration)
          setEndTime(video.duration)
          setIsLoading(false)
          console.log('✅ Video ready after retry!')
        } else {
          setIsLoading(false)
          toast.error('Invalid video file or unsupported format')
        }
      }, 1000)
    }
  }, [generateTimelineFrames])



  // Format time display
  const formatTime = useCallback((seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    const ms = Math.floor((seconds % 1) * 100)
    return `${mins}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(2, '0')}`
  }, [])

  // Handle time update
  const handleTimeUpdate = useCallback(() => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime)
    }
  }, [])

  // Handle video error
  const handleVideoError = useCallback((e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    console.error('❌ Video error:', e)
    const video = e.currentTarget
    const error = video.error
    console.error('Video error details:', error)

    let errorMessage = 'Unknown error'
    if (error) {
      switch (error.code) {
        case error.MEDIA_ERR_ABORTED:
          errorMessage = 'Video loading was aborted'
          break
        case error.MEDIA_ERR_NETWORK:
          errorMessage = 'Network error while loading video'
          break
        case error.MEDIA_ERR_DECODE:
          errorMessage = 'Video format not supported or corrupted'
          break
        case error.MEDIA_ERR_SRC_NOT_SUPPORTED:
          errorMessage = 'Video format not supported'
          break
        default:
          errorMessage = error.message || 'Unknown video error'
      }
    }

    setIsLoading(false)
    toast.error('Failed to load video: ' + errorMessage)
  }, [])

  // Handle video load start
  const handleLoadStart = useCallback(() => {
    console.log('📥 Video load started')
  }, [])

  // Handle video can play
  const handleCanPlay = useCallback(() => {
    console.log('✅ Video can play')
    const video = videoRef.current
    if (video && isLoading) {
      console.log('🎬 Video can play, checking if metadata is loaded...')
      if (video.duration && !isNaN(video.duration) && video.duration > 0) {
        console.log('📊 Duration available in canPlay:', video.duration)
        setDuration(video.duration)
        setEndTime(video.duration)
        setIsLoading(false)
      }
    }
  }, [isLoading])

  // Handle video loaded data
  const handleLoadedData = useCallback(() => {
    console.log('📊 Video data loaded')
    const video = videoRef.current
    if (video && isLoading) {
      console.log('🎬 Video data loaded, checking metadata...')
      if (video.duration && !isNaN(video.duration) && video.duration > 0) {
        console.log('📊 Duration available in loadedData:', video.duration)
        setDuration(video.duration)
        setEndTime(video.duration)
        setIsLoading(false)
      }
    }
  }, [isLoading])

  // Toggle play/pause
  const togglePlay = useCallback(() => {
    const video = videoRef.current
    if (!video) return

    if (isPlaying) {
      video.pause()
    } else {
      video.play()
    }
  }, [isPlaying])

  // Seek to specific time
  const seekTo = useCallback((time: number) => {
    const video = videoRef.current
    if (!video) return

    video.currentTime = Math.max(0, Math.min(time, duration))
  }, [duration])

  // Skip backward/forward
  const skipBackward = useCallback(() => {
    seekTo(currentTime - 10)
  }, [currentTime, seekTo])

  const skipForward = useCallback(() => {
    seekTo(currentTime + 10)
  }, [currentTime, seekTo])

  // Handle volume change
  const handleVolumeChange = useCallback((newVolume: number) => {
    const video = videoRef.current
    if (!video) return

    setVolume(newVolume)
    video.volume = newVolume
    setIsMuted(newVolume === 0)
  }, [])

  // Toggle mute
  const toggleMute = useCallback(() => {
    const video = videoRef.current
    if (!video) return

    if (isMuted) {
      video.volume = volume
      setIsMuted(false)
    } else {
      video.volume = 0
      setIsMuted(true)
    }
  }, [isMuted, volume])

  // Handle playback speed change
  const handleSpeedChange = useCallback((speed: number) => {
    const video = videoRef.current
    if (!video) return

    setPlaybackSpeed(speed)
    video.playbackRate = speed
  }, [])

  // Handle timeline click
  const handleTimelineClick = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect()
    const clickX = e.clientX - rect.left
    const percentage = clickX / rect.width
    const time = percentage * duration
    seekTo(time)
  }, [duration, seekTo])

  // Handle trim start/end changes
  const handleTrimStartChange = useCallback((time: number) => {
    setStartTime(Math.max(0, Math.min(time, endTime - 0.1)))
  }, [endTime])

  const handleTrimEndChange = useCallback((time: number) => {
    setEndTime(Math.max(startTime + 0.1, Math.min(time, duration)))
  }, [startTime, duration])

  // Handle drag start
  const handleDragStart = useCallback((type: 'start' | 'end', e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    setIsDragging(type)
    setDragStartX(e.clientX)
    setDragStartTime(type === 'start' ? startTime : endTime)
  }, [startTime, endTime])

  // Handle drag move
  const handleDragMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !timelineRef.current) return

    const rect = timelineRef.current.getBoundingClientRect()
    const deltaX = e.clientX - dragStartX
    const deltaTime = (deltaX / rect.width) * duration
    const newTime = dragStartTime + deltaTime

    if (isDragging === 'start') {
      handleTrimStartChange(newTime)
    } else {
      handleTrimEndChange(newTime)
    }
  }, [isDragging, dragStartX, dragStartTime, duration, handleTrimStartChange, handleTrimEndChange])

  // Handle drag end
  const handleDragEnd = useCallback(() => {
    setIsDragging(null)
    setDragStartX(0)
    setDragStartTime(0)
  }, [])

  // Add global mouse event listeners for dragging
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleDragMove)
      document.addEventListener('mouseup', handleDragEnd)

      return () => {
        document.removeEventListener('mousemove', handleDragMove)
        document.removeEventListener('mouseup', handleDragEnd)
      }
    }
  }, [isDragging, handleDragMove, handleDragEnd])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Prevent default if we're handling the key
      const handled = true

      switch (e.code) {
        case 'Space':
          e.preventDefault()
          togglePlay()
          break
        case 'ArrowLeft':
          e.preventDefault()
          if (e.shiftKey) {
            seekTo(currentTime - 1) // Frame by frame (1 second)
          } else {
            skipBackward() // 10 seconds
          }
          break
        case 'ArrowRight':
          e.preventDefault()
          if (e.shiftKey) {
            seekTo(currentTime + 1) // Frame by frame (1 second)
          } else {
            skipForward() // 10 seconds
          }
          break
        case 'KeyM':
          e.preventDefault()
          toggleMute()
          break
        case 'KeyI':
          e.preventDefault()
          handleTrimStartChange(currentTime)
          break
        case 'KeyO':
          e.preventDefault()
          handleTrimEndChange(currentTime)
          break
        case 'Digit1':
          e.preventDefault()
          handleSpeedChange(0.5)
          break
        case 'Digit2':
          e.preventDefault()
          handleSpeedChange(1)
          break
        case 'Digit3':
          e.preventDefault()
          handleSpeedChange(1.5)
          break
        case 'Digit4':
          e.preventDefault()
          handleSpeedChange(2)
          break
        default:
          // Key not handled
          return
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [togglePlay, currentTime, seekTo, skipBackward, skipForward, toggleMute, handleTrimStartChange, handleTrimEndChange, handleSpeedChange])

  // Trim and export video
  const handleTrimAndExport = useCallback(async () => {
    if (!isFFmpegLoaded) {
      toast.error('Video editor is still loading. Please wait.')
      return
    }

    if (startTime >= endTime) {
      toast.error('End time must be greater than start time')
      return
    }

    try {
      setIsProcessing(true)
      setProcessingProgress(0)

      const ffmpeg = ffmpegRef.current
      const inputFileName = 'input.mp4'
      const outputFileName = 'output.mp4'

      // Write input file
      await ffmpeg.writeFile(inputFileName, await fetchFile(file))

      // Calculate trim duration
      const trimDuration = endTime - startTime

      // Run FFmpeg trim command
      await ffmpeg.exec([
        '-i', inputFileName,
        '-ss', startTime.toString(),
        '-t', trimDuration.toString(),
        '-c', 'copy',
        '-avoid_negative_ts', 'make_zero',
        outputFileName
      ])

      // Read output file
      const outputData = await ffmpeg.readFile(outputFileName)
      const outputBlob = new Blob([outputData], { type: 'video/mp4' })

      // Clean up
      await ffmpeg.deleteFile(inputFileName)
      await ffmpeg.deleteFile(outputFileName)

      setIsProcessing(false)
      setProcessingProgress(0)

      onEditComplete(outputBlob)
      toast.success(`Video trimmed successfully! Duration: ${formatTime(trimDuration)}`)

    } catch (error) {
      console.error('Video trimming failed:', error)
      setIsProcessing(false)
      setProcessingProgress(0)
      toast.error('Failed to trim video: ' + (error as Error).message)
    }
  }, [isFFmpegLoaded, startTime, endTime, file, onEditComplete, formatTime])

  if (isLoading) {
    return (
      <div className="w-full h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-white text-lg">Loading video...</p>
          <p className="text-gray-400 text-sm mt-2">File: {file.name}</p>
          <p className="text-gray-400 text-xs mt-1">Size: {(file.size / 1024 / 1024).toFixed(2)} MB</p>
          {!isFFmpegLoaded && (
            <p className="text-yellow-400 text-xs mt-2">Loading video editor engine...</p>
          )}
          {videoUrl && (
            <p className="text-green-400 text-xs mt-1">Video URL created ✓</p>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="w-full h-screen bg-gray-900 text-white flex flex-col">
      {/* Header */}
      <div className="h-14 bg-gray-800 border-b border-gray-700 flex items-center justify-between px-4">
        <div className="flex items-center space-x-3">
          <FileVideo className="w-5 h-5 text-blue-400" />
          <span className="font-medium truncate max-w-xs">{file.name}</span>
          {!isFFmpegLoaded && (
            <div className="flex items-center space-x-2 text-xs text-yellow-400">
              <div className="w-3 h-3 border border-yellow-400 border-t-transparent rounded-full animate-spin"></div>
              <span>Loading editor...</span>
            </div>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setIsFullscreen(!isFullscreen)}
            className="p-2 hover:bg-gray-700 rounded"
          >
            {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
          </button>
        </div>
      </div>

      {/* Main Content - Three Column Layout */}
      <div className="flex-1 flex">
        {/* Left Panel - Video Preview */}
        <div className="flex-1 bg-black flex flex-col">
          {/* Video Container */}
          <div className="flex-1 flex items-center justify-center p-4">
            <div className="relative max-w-full max-h-full">
              <video
                ref={videoRef}
                src={videoUrl}
                className="max-w-full max-h-full object-contain"
                onLoadedMetadata={handleLoadedMetadata}
                onTimeUpdate={handleTimeUpdate}
                onPlay={() => setIsPlaying(true)}
                onPause={() => setIsPlaying(false)}
                onError={handleVideoError}
                onLoadStart={handleLoadStart}
                onCanPlay={handleCanPlay}
                onLoadedData={handleLoadedData}
                playsInline
                preload="metadata"
                crossOrigin="anonymous"
              />
              
              {/* Video Overlay Controls */}
              <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                <button
                  onClick={togglePlay}
                  className="w-16 h-16 bg-black/50 rounded-full flex items-center justify-center hover:bg-black/70 transition-colors"
                >
                  {isPlaying ? <Pause className="w-8 h-8" /> : <Play className="w-8 h-8 ml-1" />}
                </button>
              </div>
            </div>
          </div>

          {/* Video Controls */}
          <div className="h-20 bg-gray-800 border-t border-gray-700 flex items-center justify-between px-6">
            <div className="flex items-center space-x-4">
              <button onClick={skipBackward} className="p-2 hover:bg-gray-700 rounded">
                <SkipBack className="w-5 h-5" />
              </button>
              <button onClick={togglePlay} className="p-3 bg-blue-600 hover:bg-blue-700 rounded-full">
                {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5 ml-0.5" />}
              </button>
              <button onClick={skipForward} className="p-2 hover:bg-gray-700 rounded">
                <SkipForward className="w-5 h-5" />
              </button>
            </div>

            <div className="flex items-center space-x-4">
              {/* Time Display */}
              <div className="text-sm font-mono bg-gray-700 px-2 py-1 rounded">
                {formatTime(currentTime)} / {formatTime(duration)}
              </div>

              {/* Volume Control */}
              <div className="flex items-center space-x-2">
                <button onClick={toggleMute} className="p-1 hover:bg-gray-700 rounded">
                  {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
                </button>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={isMuted ? 0 : volume}
                  onChange={(e) => handleVolumeChange(Number(e.target.value))}
                  className="w-20 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                />
              </div>

              {/* Speed Control */}
              <select
                value={playbackSpeed}
                onChange={(e) => handleSpeedChange(Number(e.target.value))}
                className="bg-gray-700 border border-gray-600 rounded px-2 py-1 text-sm"
              >
                {PLAYBACK_SPEEDS.map(speed => (
                  <option key={speed.value} value={speed.value}>
                    {speed.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Right Panel - Tools */}
        <div className="w-80 bg-gray-800 border-l border-gray-700 flex flex-col">
          {/* Panel Tabs */}
          <div className="h-12 border-b border-gray-700 flex">
            <button
              onClick={() => setActivePanel('trim')}
              className={`flex-1 flex items-center justify-center space-x-2 text-sm font-medium ${
                activePanel === 'trim' ? 'bg-gray-700 text-blue-400' : 'hover:bg-gray-700'
              }`}
            >
              <Scissors className="w-4 h-4" />
              <span>Trim</span>
            </button>
            <button
              onClick={() => setActivePanel('info')}
              className={`flex-1 flex items-center justify-center space-x-2 text-sm font-medium ${
                activePanel === 'info' ? 'bg-gray-700 text-blue-400' : 'hover:bg-gray-700'
              }`}
            >
              <Info className="w-4 h-4" />
              <span>Info</span>
            </button>
          </div>

          {/* Panel Content */}
          <div className="flex-1 p-4 overflow-y-auto">
            {activePanel === 'trim' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold">Video Trimming</h3>
                
                {/* Trim Controls */}
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Start Time: {formatTime(startTime)}
                    </label>
                    <input
                      type="range"
                      min={0}
                      max={duration}
                      step={0.1}
                      value={startTime}
                      onChange={(e) => handleTrimStartChange(Number(e.target.value))}
                      className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      End Time: {formatTime(endTime)}
                    </label>
                    <input
                      type="range"
                      min={0}
                      max={duration}
                      step={0.1}
                      value={endTime}
                      onChange={(e) => handleTrimEndChange(Number(e.target.value))}
                      className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                    />
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="space-y-2">
                  <button
                    onClick={() => seekTo(startTime)}
                    className="w-full px-3 py-2 bg-green-600 hover:bg-green-700 rounded text-sm"
                  >
                    Go to Start
                  </button>
                  <button
                    onClick={() => seekTo(endTime)}
                    className="w-full px-3 py-2 bg-green-600 hover:bg-green-700 rounded text-sm"
                  >
                    Go to End
                  </button>
                </div>

                {/* Export */}
                <div className="pt-4 border-t border-gray-700">
                  <div className="text-sm text-gray-400 mb-3">
                    Trim Duration: {formatTime(endTime - startTime)}
                  </div>
                  <button
                    onClick={handleTrimAndExport}
                    disabled={isProcessing || !isFFmpegLoaded}
                    className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 rounded flex items-center justify-center space-x-2"
                  >
                    <Download className="w-4 h-4" />
                    <span>
                      {isProcessing ? `Processing... ${processingProgress}%` : 'Export Trimmed Video'}
                    </span>
                  </button>
                </div>
              </div>
            )}

            {activePanel === 'info' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold">Video Information</h3>
                
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Duration:</span>
                    <span>{formatTime(duration)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">File Size:</span>
                    <span>{(file.size / 1024 / 1024).toFixed(2)} MB</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Format:</span>
                    <span>{file.type}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Current Time:</span>
                    <span>{formatTime(currentTime)}</span>
                  </div>
                </div>

                <div className="mt-6 pt-4 border-t border-gray-700">
                  <h4 className="text-sm font-medium mb-3">Keyboard Shortcuts</h4>
                  <div className="space-y-1 text-xs text-gray-400">
                    <div className="flex justify-between">
                      <span>Space</span>
                      <span>Play/Pause</span>
                    </div>
                    <div className="flex justify-between">
                      <span>← →</span>
                      <span>Skip 10s</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Shift + ← →</span>
                      <span>Skip 1s</span>
                    </div>
                    <div className="flex justify-between">
                      <span>I / O</span>
                      <span>Set trim points</span>
                    </div>
                    <div className="flex justify-between">
                      <span>M</span>
                      <span>Mute/Unmute</span>
                    </div>
                    <div className="flex justify-between">
                      <span>1-4</span>
                      <span>Speed control</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Timeline */}
      <div className="h-32 bg-gray-800 border-t border-gray-700">
        {/* Timeline Header */}
        <div className="h-8 bg-gray-700 border-b border-gray-600 flex items-center justify-between px-4">
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium">Timeline</span>
            {isGeneratingFrames && (
              <div className="flex items-center space-x-2 text-xs text-gray-400">
                <div className="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                <span>Generating frames...</span>
              </div>
            )}
          </div>

          {/* Timeline Zoom Controls */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setTimelineZoom(Math.max(25, timelineZoom - 25))}
              className="p-1 hover:bg-gray-600 rounded"
            >
              <ZoomOut className="w-3 h-3" />
            </button>
            <span className="text-xs text-gray-400 w-12 text-center">{timelineZoom}%</span>
            <button
              onClick={() => setTimelineZoom(Math.min(400, timelineZoom + 25))}
              className="p-1 hover:bg-gray-600 rounded"
            >
              <ZoomIn className="w-3 h-3" />
            </button>
          </div>
        </div>

        {/* Timeline Content */}
        <div className="h-24 relative overflow-x-auto">
          <div
            ref={timelineRef}
            className="h-full relative cursor-pointer"
            style={{ width: `${(timelineZoom / 100) * 100}%`, minWidth: '100%' }}
            onClick={handleTimelineClick}
          >
            {/* Timeline Background */}
            <div className="absolute inset-0 bg-gray-600">
              {/* Time Markers */}
              <div className="absolute inset-0">
                {Array.from({ length: Math.ceil(duration) + 1 }, (_, i) => (
                  <div
                    key={i}
                    className="absolute top-0 bottom-0 border-l border-gray-500"
                    style={{ left: `${(i / duration) * 100}%` }}
                  >
                    <div className="absolute top-1 left-1 text-xs text-gray-300">
                      {formatTime(i)}
                    </div>
                  </div>
                ))}
              </div>

              {/* Frame Thumbnails */}
              <div className="absolute top-6 bottom-0 left-0 right-0 flex">
                {timelineFrames.map((frame, index) => (
                  <div
                    key={index}
                    className="relative flex-1 h-full border-r border-gray-500 hover:border-blue-400 transition-colors group"
                    style={{ minWidth: '80px' }}
                    onClick={(e) => {
                      e.stopPropagation()
                      seekTo(frame.time)
                    }}
                  >
                    <img
                      src={frame.thumbnail}
                      alt={`Frame ${index}`}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                    />
                    <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-xs text-white text-center py-1">
                      {formatTime(frame.time)}
                    </div>
                  </div>
                ))}
              </div>

              {/* Trim Range Overlay */}
              <div
                className="absolute top-6 bottom-0 bg-blue-500/20 border-l-2 border-r-2 border-blue-500"
                style={{
                  left: `${(startTime / duration) * 100}%`,
                  width: `${((endTime - startTime) / duration) * 100}%`
                }}
              >
                {/* Trim Handles */}
                <div
                  className="absolute top-0 bottom-0 -left-1 w-2 bg-green-500 cursor-ew-resize hover:bg-green-400"
                  onMouseDown={(e) => handleDragStart('start', e)}
                />
                <div
                  className="absolute top-0 bottom-0 -right-1 w-2 bg-green-500 cursor-ew-resize hover:bg-green-400"
                  onMouseDown={(e) => handleDragStart('end', e)}
                />
              </div>

              {/* Playhead */}
              <div
                className="absolute top-0 bottom-0 w-0.5 bg-red-500 z-10 pointer-events-none"
                style={{ left: `${(currentTime / duration) * 100}%` }}
              >
                <div className="absolute -top-1 -left-2 w-4 h-4 bg-red-500 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Hidden canvas for frame generation */}
      <canvas ref={frameCanvasRef} style={{ display: 'none' }} />
    </div>
  )
}
