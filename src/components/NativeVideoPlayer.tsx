'use client'

import { useState, useRef, useEffect } from 'react'
import { <PERSON>, Sciss<PERSON>, Ski<PERSON><PERSON><PERSON>, SkipForward, Play, Pause, Download, Loader } from 'lucide-react'
import toast from 'react-hot-toast'
import { FFmpeg } from '@ffmpeg/ffmpeg'
import { fetchFile, toBlobURL } from '@ffmpeg/util'

interface NativeVideoPlayerProps {
  file: File
  onEditComplete: (blob: Blob) => void
}

export function NativeVideoPlayer({ file, onEditComplete }: NativeVideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const ffmpegRef = useRef<FFmpeg>(new FFmpeg())

  const [isLoading, setIsLoading] = useState(true)
  const [isFFmpegLoaded, setIsFFmpegLoaded] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [duration, setDuration] = useState(0)
  const [videoUrl, setVideoUrl] = useState<string>('')
  const [currentTime, setCurrentTime] = useState(0)
  const [startTime, setStartTime] = useState(0)
  const [endTime, setEndTime] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const [processingProgress, setProcessingProgress] = useState(0)

  // Initialize FFmpeg
  useEffect(() => {
    const loadFFmpeg = async () => {
      try {
        console.log('🔧 Loading FFmpeg...')
        const ffmpeg = ffmpegRef.current

        // Load FFmpeg with CDN URLs
        const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd'
        await ffmpeg.load({
          coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
          wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
        })

        // Set up progress monitoring
        ffmpeg.on('progress', ({ progress }) => {
          setProcessingProgress(Math.round(progress * 100))
        })

        ffmpeg.on('log', ({ message }) => {
          console.log('FFmpeg:', message)
        })

        setIsFFmpegLoaded(true)
        console.log('✅ FFmpeg loaded successfully!')
        toast.success('Video editor ready!')
      } catch (error) {
        console.error('❌ Failed to load FFmpeg:', error)
        toast.error('Failed to load video editor. Some features may not work.')
      }
    }

    loadFFmpeg()
  }, [])

  useEffect(() => {
    console.log('NativeVideoPlayer: Creating URL for file:', file.name)
    const url = URL.createObjectURL(file)
    setVideoUrl(url)
    console.log('Video URL created:', url)

    return () => {
      console.log('Cleaning up video URL')
      URL.revokeObjectURL(url)
    }
  }, [file])

  const handleLoadedMetadata = () => {
    console.log('✅ Native video metadata loaded!')
    const video = videoRef.current
    if (video && video.duration && !isNaN(video.duration)) {
      console.log('Duration:', video.duration)
      setDuration(video.duration)
      setEndTime(video.duration) // Set default end time to full duration
      setIsLoading(false)
      console.log('✅ Native video is ready!')
    } else {
      console.error('❌ Invalid video duration:', video?.duration)
      setIsLoading(false)
      toast.error('Invalid video file')
    }
  }

  const handleError = (e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    console.error('❌ Native video error:', e)
    const video = e.currentTarget
    console.error('Video error details:', video.error)
    setIsLoading(false)
    toast.error('Failed to load video: ' + (video.error?.message || 'Unknown error'))
  }

  const handleLoadStart = () => {
    console.log('📥 Native video load started')
  }

  const handleCanPlay = () => {
    console.log('✅ Native video can play')
  }

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime)
    }
  }

  const handlePlay = () => {
    setIsPlaying(true)
  }

  const handlePause = () => {
    setIsPlaying(false)
  }

  const seekTo = (time: number) => {
    if (videoRef.current) {
      videoRef.current.currentTime = Math.max(0, Math.min(time, duration))
    }
  }

  const togglePlay = () => {
    if (!videoRef.current) return

    if (isPlaying) {
      videoRef.current.pause()
    } else {
      videoRef.current.play()
    }
  }

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const handleTrimVideo = async () => {
    if (!isFFmpegLoaded) {
      toast.error('Video editor is still loading. Please wait.')
      return
    }

    if (startTime >= endTime) {
      toast.error('End time must be greater than start time')
      return
    }

    try {
      setIsProcessing(true)
      setProcessingProgress(0)

      console.log('🎬 Starting video trimming with FFmpeg...')
      console.log('Trim range:', startTime, 'to', endTime)

      const ffmpeg = ffmpegRef.current

      // Write input file to FFmpeg filesystem
      const inputFileName = 'input.mp4'
      const outputFileName = 'output.mp4'

      console.log('📁 Writing input file to FFmpeg filesystem...')
      await ffmpeg.writeFile(inputFileName, await fetchFile(file))

      // Calculate trim duration
      const trimDuration = endTime - startTime

      // Run FFmpeg trim command
      console.log('⚡ Running FFmpeg trim command...')
      await ffmpeg.exec([
        '-i', inputFileName,
        '-ss', startTime.toString(),
        '-t', trimDuration.toString(),
        '-c', 'copy', // Use stream copy for faster processing
        '-avoid_negative_ts', 'make_zero',
        outputFileName
      ])

      // Read the output file
      console.log('📤 Reading trimmed video...')
      const outputData = await ffmpeg.readFile(outputFileName)

      // Create blob from output data
      const outputBlob = new Blob([outputData], { type: 'video/mp4' })

      // Clean up FFmpeg filesystem
      await ffmpeg.deleteFile(inputFileName)
      await ffmpeg.deleteFile(outputFileName)

      setIsProcessing(false)
      setProcessingProgress(0)

      console.log('✅ Video trimming completed!')
      onEditComplete(outputBlob)
      toast.success(`Video trimmed successfully! New duration: ${formatTime(trimDuration)}`)

    } catch (error) {
      console.error('❌ Video trimming failed:', error)
      setIsProcessing(false)
      setProcessingProgress(0)
      toast.error('Failed to trim video: ' + (error as Error).message)
    }
  }

  const handleExportOriginal = () => {
    console.log('📤 Exporting original video...')
    const blob = new Blob([file], { type: file.type })
    onEditComplete(blob)
    toast.success('Original video exported successfully!')
  }



  return (
    <div className="w-full max-w-4xl mx-auto p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      <h2 className="text-xl font-bold mb-4 text-gray-900 dark:text-gray-100 flex items-center">
        <Scissors className="w-6 h-6 mr-2" />
        Professional Video Editor - {file.name}
      </h2>

      {/* FFmpeg Status */}
      {!isFFmpegLoaded && (
        <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
          <div className="flex items-center">
            <Loader className="w-4 h-4 mr-2 animate-spin text-yellow-600" />
            <span className="text-sm text-yellow-800 dark:text-yellow-200">
              Loading video editor engine...
            </span>
          </div>
        </div>
      )}

      {isLoading && (
        <div className="flex items-center justify-center h-64 bg-gray-100 dark:bg-gray-700 rounded-lg">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading video...</p>
            <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
              File: {file.name} ({Math.round(file.size / 1024 / 1024)}MB)
            </p>
            <p className="text-xs text-gray-400 mt-1">
              Type: {file.type}
            </p>
          </div>
        </div>
      )}

      {/* Always show video element, even when loading */}
      <div className="relative bg-black rounded-lg overflow-hidden mb-6">
        <video
          ref={videoRef}
          src={videoUrl}
          className="w-full h-auto max-h-96 object-contain"
          controls={true}  // Use native controls for simplicity
          playsInline
          preload="metadata"
          onLoadedMetadata={handleLoadedMetadata}
          onTimeUpdate={handleTimeUpdate}
          onPlay={handlePlay}
          onPause={handlePause}
          onError={handleError}
          onLoadStart={handleLoadStart}
          onCanPlay={handleCanPlay}
          style={{ display: isLoading ? 'none' : 'block' }}
        />
        
        {!isLoading && (
          <div className="absolute top-4 right-4 flex space-x-2">
            <button
              onClick={handleExportOriginal}
              className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2 text-sm shadow-lg"
              disabled={isProcessing}
            >
              <Download className="w-4 h-4" />
              <span>Export Original</span>
            </button>

            <button
              onClick={handleTrimVideo}
              className="px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center space-x-2 text-sm shadow-lg"
              disabled={!isFFmpegLoaded || isProcessing}
            >
              <Scissors className="w-4 h-4" />
              <span>Trim & Export</span>
            </button>
          </div>
        )}
      </div>

      {/* Processing Progress */}
      {isProcessing && (
        <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
              Processing video...
            </span>
            <span className="text-sm text-blue-600 dark:text-blue-400">
              {processingProgress}%
            </span>
          </div>
          <div className="w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${processingProgress}%` }}
            ></div>
          </div>
          <p className="text-xs text-blue-700 dark:text-blue-300 mt-2">
            This may take a few minutes depending on video length and your device performance.
          </p>
        </div>
      )}

      {!isLoading && (
        <>
          {/* Video Editing Controls */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
            <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100 flex items-center">
              <Scissors className="w-5 h-5 mr-2" />
              Video Editing
            </h3>

            {/* Custom Controls */}
            <div className="mb-4 p-3 bg-white dark:bg-gray-800 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Custom Controls</h4>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => seekTo(Math.max(0, currentTime - 10))}
                  className="flex items-center space-x-1 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm"
                >
                  <SkipBack className="w-4 h-4" />
                  <span>-10s</span>
                </button>

                <button
                  onClick={togglePlay}
                  className="flex items-center space-x-1 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 text-sm"
                >
                  {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                  <span>{isPlaying ? 'Pause' : 'Play'}</span>
                </button>

                <button
                  onClick={() => seekTo(Math.min(duration, currentTime + 10))}
                  className="flex items-center space-x-1 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm"
                >
                  <SkipForward className="w-4 h-4" />
                  <span>+10s</span>
                </button>

                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Current: {formatTime(currentTime)} / {formatTime(duration)}
                </div>
              </div>
            </div>

            {/* Trim Controls */}
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Trim Video</h4>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                    Start Time: {formatTime(startTime)}
                  </label>
                  <input
                    type="range"
                    min={0}
                    max={duration}
                    value={startTime}
                    onChange={(e) => {
                      const value = Number(e.target.value)
                      setStartTime(Math.min(value, endTime - 1))
                    }}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-600"
                  />
                </div>

                <div>
                  <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                    End Time: {formatTime(endTime)}
                  </label>
                  <input
                    type="range"
                    min={0}
                    max={duration}
                    value={endTime}
                    onChange={(e) => {
                      const value = Number(e.target.value)
                      setEndTime(Math.max(value, startTime + 1))
                    }}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-600"
                  />
                </div>
              </div>

              <div className="flex space-x-2 mt-3">
                <button
                  onClick={() => seekTo(startTime)}
                  className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
                >
                  Go to Start
                </button>
                <button
                  onClick={() => seekTo(endTime)}
                  className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
                >
                  Go to End
                </button>
                <div className="flex-1"></div>
                <div className="text-sm text-gray-600 dark:text-gray-400 flex items-center">
                  Trim Duration: {formatTime(endTime - startTime)}
                </div>
              </div>
            </div>
          </div>

          {/* Video Information */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-gray-100">Video Information</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Original Duration:</span>
              <span className="ml-2 text-gray-600 dark:text-gray-400">{formatTime(duration)}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Trim Duration:</span>
              <span className="ml-2 text-blue-600 dark:text-blue-400 font-medium">{formatTime(endTime - startTime)}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">File Size:</span>
              <span className="ml-2 text-gray-600 dark:text-gray-400">{Math.round(file.size / 1024 / 1024)}MB</span>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Format:</span>
              <span className="ml-2 text-gray-600 dark:text-gray-400">{file.type}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Current Time:</span>
              <span className="ml-2 text-gray-600 dark:text-gray-400">{formatTime(currentTime)}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700 dark:text-gray-300">Status:</span>
              <span className="ml-2 text-green-600 dark:text-green-400">Ready for Editing</span>
            </div>
          </div>
          
          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <p className="text-sm text-blue-800 dark:text-blue-200">
              <strong>Video Editor:</strong> Use the native video controls for playback, or use the custom controls above for precise editing.
              Set trim points using the sliders and use the navigation buttons to preview your edits.
            </p>
          </div>
        </div>
        </>
      )}
    </div>
  )
}
