'use client'

import { useState, useCallback } from 'react'
import { FileUpload } from '@/components/FileUpload'
import toast from 'react-hot-toast'

export default function TestUploadPage() {
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([])

  const handleFileUpload = useCallback((files: File[]) => {
    console.log('Files received in test page:', files)
    setUploadedFiles(files)
    toast.success(`Uploaded ${files.length} file(s)`)
  }, [])

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-8">
          File Upload Test
        </h1>

        <div className="mb-8">
          <FileUpload
            onFileUpload={handleFileUpload}
            acceptedTypes={{
              'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp'],
              'video/*': ['.mp4', '.avi', '.mov', '.webm']
            }}
            maxFileSize={100 * 1024 * 1024} // 100MB
            title="Drop test files here"
            subtitle="Images and videos supported"
          />
        </div>

        {uploadedFiles.length > 0 && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Uploaded Files ({uploadedFiles.length})
            </h2>
            
            <div className="space-y-4">
              {uploadedFiles.map((file, index) => (
                <div key={index} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-gray-100">
                        {file.name}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Type: {file.type} | Size: {(file.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                    
                    <div className="flex space-x-2">
                      {file.type.startsWith('image/') && (
                        <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden">
                          <img
                            src={URL.createObjectURL(file)}
                            alt={file.name}
                            className="w-full h-full object-cover"
                            onLoad={(e) => {
                              // Clean up object URL after image loads
                              setTimeout(() => {
                                URL.revokeObjectURL((e.target as HTMLImageElement).src)
                              }, 1000)
                            }}
                          />
                        </div>
                      )}
                      
                      {file.type.startsWith('video/') && (
                        <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden">
                          <video
                            src={URL.createObjectURL(file)}
                            className="w-full h-full object-cover"
                            muted
                            onLoadedData={(e) => {
                              // Clean up object URL after video loads
                              setTimeout(() => {
                                URL.revokeObjectURL((e.target as HTMLVideoElement).src)
                              }, 1000)
                            }}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="mt-4 flex space-x-2">
                    <button
                      onClick={() => {
                        if (file.type.startsWith('image/')) {
                          window.open(`/image-editor`, '_blank')
                        } else {
                          alert('Video editor has been removed')
                        }
                      }}
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Open in Editor
                    </button>
                    
                    <button
                      onClick={() => {
                        const url = URL.createObjectURL(file)
                        const a = document.createElement('a')
                        a.href = url
                        a.download = file.name
                        document.body.appendChild(a)
                        a.click()
                        document.body.removeChild(a)
                        URL.revokeObjectURL(url)
                      }}
                      className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                    >
                      Download
                    </button>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
              <button
                onClick={() => setUploadedFiles([])}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Clear All Files
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
