'use client'

import { useState, useCallback, useEffect } from 'react'
import { motion } from 'framer-motion'
import { FileUpload } from '@/components/FileUpload'
import { FixedVideoEditor } from '@/components/FixedVideoEditor'
import { AuthModal } from '@/components/AuthModal'
import { getUploadedFile, clearUploadedFile, getFileCategory, base64ToFile } from '@/utils/fileHandler'
import toast from 'react-hot-toast'
import {
  Scissors,
  Music,
  Volume2,
  Film
} from 'lucide-react'

interface UploadedFile {
  id: string
  name: string
  size: number
  type: string
  file: File
  originalSize: number
  status: 'pending' | 'editing' | 'completed' | 'error'
  editedFile?: Blob
  downloadUrl?: string
}

export default function VideoEditorPage() {
  const [files, setFiles] = useState<UploadedFile[]>([])
  const [currentFile, setCurrentFile] = useState<UploadedFile | null>(null)
  const [showAuthModal, setShowAuthModal] = useState(false)

  const handleFileUpload = useCallback((uploadedFiles: File[]) => {
    console.log('Files uploaded:', uploadedFiles)
    const videoFiles = uploadedFiles.filter(file => file.type.startsWith('video/'))
    console.log('Video files filtered:', videoFiles)

    if (videoFiles.length === 0) {
      toast.error('Please upload video files only')
      return
    }

    const newFiles: UploadedFile[] = videoFiles.map(file => ({
      id: Math.random().toString(36).substring(2, 11),
      name: file.name,
      size: file.size,
      type: file.type,
      file,
      originalSize: file.size,
      status: 'pending'
    }))

    console.log('New files created:', newFiles)
    setFiles(prev => [...prev, ...newFiles])

    // Auto-select first file for editing
    if (newFiles.length > 0 && !currentFile) {
      console.log('Setting current file:', newFiles[0])
      setCurrentFile(newFiles[0])
    }
  }, [currentFile])

  // Check for uploaded file from home page
  useEffect(() => {
    const uploadedFile = getUploadedFile()
    if (uploadedFile && getFileCategory(uploadedFile.type) === 'video') {
      const file = base64ToFile(uploadedFile.data, uploadedFile.name, uploadedFile.type, Date.now())
      handleFileUpload([file])
      clearUploadedFile()
    }
  }, [handleFileUpload])

  const handleFileSelect = (file: UploadedFile) => {
    setCurrentFile(file)
  }

  const handleEditComplete = (editedBlob: Blob) => {
    if (!currentFile) return

    const downloadUrl = URL.createObjectURL(editedBlob)
    
    setFiles(prev => prev.map(file => 
      file.id === currentFile.id 
        ? { 
            ...file, 
            status: 'completed',
            editedFile: editedBlob,
            downloadUrl 
          }
        : file
    ))

    setCurrentFile(prev => prev ? {
      ...prev,
      status: 'completed',
      editedFile: editedBlob,
      downloadUrl
    } : null)

    toast.success('Video edited successfully!')
  }



  const clearAllFiles = () => {
    files.forEach(file => {
      if (file.downloadUrl) {
        URL.revokeObjectURL(file.downloadUrl)
      }
    })
    setFiles([])
    setCurrentFile(null)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center mr-4">
              <Scissors className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                Video Editor
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300">
                Professional video editing in your browser
              </p>
            </div>
          </div>

          {/* Features */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
            <div className="flex flex-col items-center p-4 bg-white/50 dark:bg-gray-800/50 rounded-xl backdrop-blur-sm">
              <Scissors className="w-6 h-6 text-blue-500 mb-2" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Trim & Cut</span>
            </div>
            <div className="flex flex-col items-center p-4 bg-white/50 dark:bg-gray-800/50 rounded-xl backdrop-blur-sm">
              <Music className="w-6 h-6 text-purple-500 mb-2" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Add Audio</span>
            </div>
            <div className="flex flex-col items-center p-4 bg-white/50 dark:bg-gray-800/50 rounded-xl backdrop-blur-sm">
              <Volume2 className="w-6 h-6 text-green-500 mb-2" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Volume Control</span>
            </div>
            <div className="flex flex-col items-center p-4 bg-white/50 dark:bg-gray-800/50 rounded-xl backdrop-blur-sm">
              <Film className="w-6 h-6 text-orange-500 mb-2" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Effects</span>
            </div>
          </div>
        </motion.div>

        {files.length === 0 ? (
          /* Upload Section */
          <motion.div
            initial={{ opacity: 1, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <FileUpload
              onFileUpload={handleFileUpload}
              acceptedTypes={{
                'video/mp4': ['.mp4'],
                'video/avi': ['.avi'],
                'video/mov': ['.mov'],
                'video/webm': ['.webm'],
                'video/mkv': ['.mkv'],
                'video/wmv': ['.wmv']
              }}
              maxFileSize={500 * 1024 * 1024} // 500MB for videos
              title="Drop your videos here"
              subtitle="Support for MP4, AVI, MOV, WebM, MKV, WMV up to 500MB"
            />
          </motion.div>
        ) : (
          /* Professional Video Editor */
          currentFile && (
            <div className="fixed inset-0 z-50 bg-gray-900">
              {/* File Selector Overlay */}
              {files.length > 1 && (
                <div className="absolute top-4 left-4 z-10">
                  <div className="bg-gray-800 rounded-lg p-3 shadow-lg">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-sm font-medium text-white">Files:</span>
                      <button
                        onClick={clearAllFiles}
                        className="text-xs text-red-400 hover:text-red-300"
                      >
                        Clear All
                      </button>
                    </div>
                    <div className="space-y-1">
                      {files.map((file) => (
                        <button
                          key={file.id}
                          onClick={() => handleFileSelect(file)}
                          className={`block w-full text-left px-2 py-1 rounded text-sm transition-colors ${
                            currentFile?.id === file.id
                              ? 'bg-blue-600 text-white'
                              : 'text-gray-300 hover:bg-gray-700'
                          }`}
                        >
                          <div className="truncate max-w-48">{file.name}</div>
                          <div className="text-xs opacity-75">
                            {(file.size / 1024 / 1024).toFixed(2)} MB
                            {file.status === 'completed' && (
                              <span className="ml-2 text-green-400">✓</span>
                            )}
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Close Button */}
              <button
                onClick={() => {
                  setCurrentFile(null)
                  setFiles([])
                }}
                className="absolute top-4 right-4 z-10 w-10 h-10 bg-gray-800 hover:bg-gray-700 rounded-full flex items-center justify-center text-white"
              >
                ×
              </button>

              {/* Fixed Video Editor */}
              <FixedVideoEditor
                file={currentFile.file}
                onEditComplete={handleEditComplete}
              />
            </div>
          )
        )}

        {/* Auth Modal */}
        <AuthModal 
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
        />
      </div>
    </div>
  )
}
